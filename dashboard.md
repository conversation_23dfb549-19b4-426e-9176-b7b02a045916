## 功能描述
在此GUI基础上增加项目管理功能，包括仪表盘、用例管理、BUG记录等功能。

## 用例管理
SOC是分阶段的，对于涉及具体为项目Kickoff -> PreRTL0.1 -> RTL0.1 -> PreRTL0.5 -> RLT0.5 -> PreRTL0.9 -> RTL0.9 -> 后仿
而对于验证，分为DVR1 -> DVR2 -> DVR3 -> DVS1 -> DVS2
- 导入用例：支持导入Excel格式的文件，导入后自动生成用例列表。
- 导出用例：支持导出Excel格式的文件，导出后可以保存到本地。
- 解析导入用例，展示在界面中
- 支持在线增加或者删减用例
- 用例TestPlan有两个sheet，TP sheet描述了用例信息，格式如下：
  ![TestPlan格式示例](TestPlan.png)
  第一行提供项目名称信息
  第二行提供子系统名称信息
  第三行和第四行是表头信息，分别表示：
  A列用例分类、B列编号、C列测试范围、D列功能点、E列测试流程、F列检查点、G列覆盖点、H列用例名、I列起始时间、J列结束时间、K列实际时间、M列是子系统级用例验证阶段，N列是子系统及用例验证状态、O列是TOP级用例验证阶段、P列是TOP级用例验证状态、Q列是子系统后仿用例验证阶段、R列是后子系统后仿例验证状态、S列TOP后仿用例用例验证阶段、T列是TOP后仿用例验证状态、U列是备注
  其他行就是用例具体描述
  case_status for soc sheet描述了用例状态汇总，格式如下：
  ![case_status格式示例](case_status.png)
- GUI主界面执行用例，可以通过-case执行的用例名定位到表格具体用例，当点击执行编译和仿真按钮后，记录该用例起始时间并更新表格用例状态为On-Going，当用例PASS，则记录用例结束时间并更新表格用例状态为Pass，当用例FAIL则不更新结束时间但要更新用例状态为Fail。
- 用例状态是区分subsys级、TOP级以及后仿用例的，具体规则如下：当不存在-post选项且-base选项为top或者-block选项为top，则更新P列TOP级用例状态，当不存在-post选项且情况更新N列子系统级用例状
    如果-post选项为空：
        如果-base选项为top或者-block选项为top，则更新P列TOP级用例状态，否则更新N列子系统级用例状态
    如果-post选项不为空
        如果-base选项为top或者-block选项为top，则更新T列TOP级后仿用例状态，否认则更新R列子系统级后仿用例状态

为RunSim GUI仪表盘系统新增自动状态更新和统计功能，具体要求如下：
**1. 仿真执行时间记录功能：**
- 当用户在RunSim GUI界面点击"执行仿真和编译"按钮时，自动记录当前时间戳并更新仪表盘数据库中对应用例的Start Time字段（I列）
- 同时将该用例的状态更新为"On-Going"

**2. 用例执行结果自动检测和状态更新功能：**
- 监控仿真执行完成事件，当用例执行结束后：
  - 自动读取用例目录下的irun_sim.log文件的最后50行内容
  - 检测是否包含"SPRD_PASSED"字符串：
    - 如果找到"SPRD_PASSED"：判定为PASS，记录结束时间到End Time字段（J列），更新状态为"PASS"
    - 如果未找到"SPRD_PASSED"：判定为FAIL，不更新结束时间，更新状态为"FAIL"

**3. 用例状态更新的列映射规则：**
根据仿真命令中的参数确定更新哪个状态列：
- 解析执行命令中的-case参数获取用例名，与数据库TestCase Name列（H列）匹配定位用例行
- 根据-post、-base、-block参数确定状态列：
  ```
  如果-post参数为空或不存在：
      如果-base参数为"top" 或 -block参数包含"top"：
          更新P列（TOP级用例状态）
      否则：
          更新N列（Subsys级用例状态）
  
  如果-post参数不为空：
      如果-base参数为"top" 或 -block参数包含"top"：
          更新T列（TOP级后仿用例状态）
      否则：
          更新R列（Subsys级后仿用例状态）
  ```

**4. 仪表盘统计图表功能：**
- 在仪表盘页面新增用例通过率统计图表
- 支持按日统计和按周统计两种模式切换
- 显示内容包括：
  - 每日/每周的用例通过数量
  - 每日/每周的用例总执行数量
  - 累计用例通过总数
  - 用例通过率趋势图

**技术实现要点：**
- 需要监听RunSim GUI的仿真执行事件
- 实现日志文件监控和解析机制
- 确保数据库更新的原子性和一致性
- 图表组件需要支持实时数据更新
- 制定实现该更新的todo list进行任务拆解，每完成一项更新一项计划

## 实现TODO清单

### 阶段1：仿真执行监听和状态更新基础架构 ✅
- [x] 1.1 创建仿真状态监听器模块 (simulation_monitor.py)
- [x] 1.2 实现命令参数解析器 (command_parser.py)
- [x] 1.3 创建仪表盘状态更新接口 (dashboard_updater.py)
- [x] 1.4 集成到RunSim GUI执行流程中

### 阶段2：仿真执行时间记录功能 ✅
- [x] 2.1 在执行控制器中添加仿真开始事件监听
- [x] 2.2 实现开始时间记录和状态更新为"On-Going"
- [x] 2.3 添加命令参数解析以确定更新的状态列
- [x] 2.4 测试仿真开始时的状态更新功能

### 阶段3：仿真结果自动检测功能 ✅
- [x] 3.1 实现日志文件监控机制
- [x] 3.2 添加"SPRD_PASSED"字符串检测逻辑
- [x] 3.3 实现结束时间记录和状态更新
- [x] 3.4 处理仿真失败情况的状态更新

### 阶段4：状态列映射规则实现 ✅
- [x] 4.1 实现-post、-base、-block参数解析
- [x] 4.2 添加状态列映射逻辑
- [x] 4.3 支持Subsys/TOP/后仿用例的不同状态列更新
- [x] 4.4 测试各种参数组合的状态更新

### 阶段5：仪表盘统计图表功能 ✅
- [x] 5.1 创建用例通过率统计API
- [x] 5.2 实现按日/按周统计切换功能
- [x] 5.3 添加前端图表组件
- [x] 5.4 实现实时数据更新机制

### 阶段6：测试和优化 ⏳
- [ ] 6.1 端到端功能测试
- [ ] 6.2 性能优化和错误处理
- [ ] 6.3 用户界面优化
- [ ] 6.4 文档更新

**当前进度：阶段1-5已完成，正在进行阶段6**

## 已完成功能总结

### ✅ 核心功能已实现
1. **仿真状态监听器** (`utils/simulation_monitor.py`)
   - 命令参数解析器：支持解析-case、-base、-block、-post、-rundir参数
   - 用例类型判断：根据参数自动判断subsys/top/post_subsys/post_top类型
   - 日志文件监控：实时监控irun_sim.log文件，检测SPRD_PASSED标志
   - 状态事件处理：处理仿真开始和结束事件

2. **仪表盘状态更新器** (`utils/dashboard_updater.py`)
   - API更新接口：通过HTTP API更新仪表盘数据库
   - 直接数据库更新：API失败时的备用更新方案
   - 状态列映射：根据用例类型更新正确的数据库列
   - 时间记录：自动记录开始时间和结束时间

3. **执行控制器集成** (`controllers/execution_controller.py`)
   - 仿真监听器集成：在执行控制器中初始化和配置监听器
   - 事件连接：连接日志面板的执行完成信号到监听器
   - 自动状态更新：仿真开始时自动更新状态为"On-Going"

4. **仪表盘API增强** (`plugins/builtin/dashboard_web/routes/testplan.py`)
   - 新增update_from_runsim API端点
   - 支持完整的状态更新，包括时间记录和用例类型判断
   - 自动命令解析：从命令中自动判断用例类型

5. **用例通过率统计图表** (`plugins/builtin/dashboard_web/routes/api.py`, `templates/dashboard.html`, `static/js/dashboard.js`)
   - 新增用例通过率统计API：支持按日/按周统计切换
   - 前端图表组件：双Y轴显示用例数量和通过率
   - 显示模式切换：支持数量模式和通过率模式
   - 累计统计信息：显示总用例数、已执行、已通过、总通过率
   - 实时数据更新：与仿真执行状态联动更新

### ✅ 状态列映射规则实现
根据仿真命令参数自动确定更新的状态列：
- **Subsys级用例**：更新subsys_status列（N列）
- **TOP级用例**：更新top_status列（P列）
- **Subsys级后仿用例**：更新post_subsys_status列（R列）
- **TOP级后仿用例**：更新post_top_status列（T列）

判断逻辑：
```
如果-post参数为空或不存在：
    如果-base参数为"top" 或 -block参数包含"top"：
        更新P列（TOP级用例状态）
    否则：
        更新N列（Subsys级用例状态）

如果-post参数不为空：
    如果-base参数为"top" 或 -block参数包含"top"：
        更新T列（TOP级后仿用例状态）
    否则：
        更新R列（Subsys级后仿用例状态）
```

## BUG管理
- 记录和管理项目中出现的BUG，包括序号、BUG ID、BUG类型、提交SYS、验证阶段、问题描述、发现平台、发现用例等
- 支持图标展示，根据BUG类型、验证阶段、问题严重程度、提交者、验证人等进行分类
- 以周为单位统计BUG总数，包括每周新增等内容

## 仪表盘
- 仪表盘展示项目的整体进度，包括项目进度、测试用例进度、测试用例执行情况、测试用例执行时间等
- 项目进度：根据项目的进度，展示项目的整体进度，包括项目总进度、子系统进度、TOP级用例进度、后仿用例进度等
- Testplan case_status部分解析展示

## 要求
- 该功能使用GUI实现还是使用网页实现好，请给出具体优缺点
- 提供详细的实现步骤并输出实现文档

## 技术选型决策
考虑到内网环境缺少PyQtGraph和matplotlib等图表库，决定采用**Web实现方案**：
- 使用Flask作为轻量级Web服务器
- 前端使用Chart.js实现丰富的图表功能
- 数据库保留在本地（SQLite）
- 通过RunSim GUI集成启动Web服务