"""
用例管理数据模型

该模块提供用例数据的CRUD操作和业务逻辑。
"""

import os
import logging
import importlib.util
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple

# 配置日志
logger = logging.getLogger(__name__)

# 动态导入database模块，避免模块冲突
def _get_database_module():
    """获取database模块"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    database_file = os.path.join(current_dir, 'database.py')
    spec = importlib.util.spec_from_file_location("testplan_database", database_file)
    database_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(database_module)
    return database_module

# 获取get_db函数
_db_module = _get_database_module()
get_db = _db_module.get_db

class TestCaseManager:
    """用例管理器"""

    @staticmethod
    def case_name_exists(case_name: str, project_id: int = None) -> bool:
        """
        检查用例名称是否已存在

        Args:
            case_name: 用例名称
            project_id: 项目ID，如果为None则检查所有项目

        Returns:
            bool: 是否存在
        """
        try:
            with get_db() as conn:
                cursor = conn.cursor()
                
                if project_id:
                    cursor.execute("SELECT COUNT(*) as count FROM test_cases WHERE case_name = ? AND project_id = ?", 
                                 (case_name, project_id))
                else:
                    cursor.execute("SELECT COUNT(*) as count FROM test_cases WHERE case_name = ?", 
                                 (case_name,))
                
                result = cursor.fetchone()
                return result['count'] > 0

        except Exception as e:
            logger.error(f"检查用例名称是否存在失败: {str(e)}")
            return False

    @staticmethod
    def create_test_case(category: str, case_name: str, test_areas: str, test_scope: str,
                        function_point: str, check_point: str, cover: str, owner: str,
                        start_time: str = None, end_time: str = None, remarks: str = None,
                        project_id: int = None) -> int:
        """
        创建新用例（支持直接参数调用）

        Args:
            category: 用例类别
            case_name: 用例名称
            test_areas: 测试区域
            test_scope: 测试范围
            function_point: 功能点
            check_point: 检查点
            cover: 覆盖内容
            owner: 负责人
            start_time: 开始时间
            end_time: 结束时间
            remarks: 备注
            project_id: 项目ID

        Returns:
            int: 新创建用例的ID
        """
        try:
            with get_db() as conn:
                cursor = conn.cursor()

                # 如果没有指定项目ID，使用默认项目
                if project_id is None:
                    cursor.execute("SELECT id FROM projects WHERE name = '默认项目' LIMIT 1")
                    result = cursor.fetchone()
                    if result:
                        project_id = result['id']
                    else:
                        # 创建默认项目
                        cursor.execute('''
                            INSERT INTO projects (name, subsystem, description)
                            VALUES (?, ?, ?)
                        ''', ('默认项目', 'default', '系统默认项目'))
                        project_id = cursor.lastrowid

                # 插入用例数据
                cursor.execute('''
                    INSERT INTO test_cases (
                        project_id, category, number, test_areas, test_scope, function_point,
                        check_point, cover, case_name, start_time, end_time, actual_time,
                        subsys_stage, subsys_status, top_stage, top_status,
                        post_subsys_stage, post_subsys_status, post_top_stage, post_top_status,
                        remarks, test_process, coverage_point, owner
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    project_id,
                    category,
                    '',  # number
                    test_areas,
                    test_scope,
                    function_point,
                    check_point,
                    cover,
                    case_name,
                    start_time,
                    end_time,
                    None,  # actual_time
                    '',  # subsys_stage
                    'Pending',  # subsys_status
                    '',  # top_stage
                    'Pending',  # top_status
                    '',  # post_subsys_stage
                    'Pending',  # post_subsys_status
                    '',  # post_top_stage
                    'Pending',  # post_top_status
                    remarks or '',
                    '',  # test_process
                    '',  # coverage_point
                    owner
                ))

                case_id = cursor.lastrowid
                conn.commit()

                logger.info(f"创建用例成功: {case_name} (ID: {case_id})")
                return case_id

        except Exception as e:
            logger.error(f"创建用例失败: {str(e)}")
            raise

    @staticmethod
    def get_test_cases(project_id: int = None, page: int = 1, page_size: int = 50,
                      search: str = None, status_filter: str = None) -> Tuple[List[Dict], int]:
        """
        获取用例列表

        Args:
            project_id: 项目ID，如果为None则获取所有项目的用例
            page: 页码
            page_size: 每页数量
            search: 搜索关键词
            status_filter: 状态过滤

        Returns:
            Tuple[List[Dict], int]: (用例列表, 总数量)
        """
        try:
            with get_db() as conn:
                cursor = conn.cursor()

                # 构建查询条件
                where_conditions = []
                params = []

                if project_id:
                    where_conditions.append("tc.project_id = ?")
                    params.append(project_id)

                if search:
                    where_conditions.append("""
                        (tc.case_name LIKE ? OR tc.category LIKE ? OR
                         tc.test_scope LIKE ? OR tc.function_point LIKE ?)
                    """)
                    search_param = f"%{search}%"
                    params.extend([search_param] * 4)

                if status_filter:
                    where_conditions.append("""
                        (tc.subsys_status = ? OR tc.top_status = ? OR
                         tc.post_subsys_status = ? OR tc.post_top_status = ?)
                    """)
                    params.extend([status_filter] * 4)

                where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

                # 查询总数
                count_query = f"""
                    SELECT COUNT(*) as total
                    FROM test_cases tc
                    LEFT JOIN projects p ON tc.project_id = p.id
                    WHERE {where_clause}
                """
                cursor.execute(count_query, params)
                total_count = cursor.fetchone()['total']

                # 查询数据
                offset = (page - 1) * page_size
                data_query = f"""
                    SELECT
                        tc.*,
                        p.name as project_name,
                        p.subsystem as project_subsystem
                    FROM test_cases tc
                    LEFT JOIN projects p ON tc.project_id = p.id
                    WHERE {where_clause}
                    ORDER BY tc.created_at DESC
                    LIMIT ? OFFSET ?
                """
                cursor.execute(data_query, params + [page_size, offset])

                cases = []
                for row in cursor.fetchall():
                    case_dict = dict(row)
                    # 转换时间格式
                    for time_field in ['start_time', 'end_time', 'created_at', 'updated_at']:
                        if case_dict.get(time_field):
                            case_dict[time_field] = case_dict[time_field]
                    cases.append(case_dict)

                return cases, total_count

        except Exception as e:
            logger.error(f"获取用例列表失败: {str(e)}")
            raise

    @staticmethod
    def update_case_status(case_id: int, stage_type: str, status: str,
                          changed_by: str = None) -> bool:
        """
        更新用例状态

        Args:
            case_id: 用例ID
            stage_type: 阶段类型 ('subsys', 'top', 'post_subsys', 'post_top')
            status: 新状态
            changed_by: 修改人

        Returns:
            bool: 是否更新成功
        """
        try:
            # 验证stage_type参数
            valid_stages = ['subsys', 'top', 'post_subsys', 'post_top']
            if stage_type not in valid_stages:
                logger.error(f"无效的阶段类型: {stage_type}")
                return False

            with get_db() as conn:
                cursor = conn.cursor()

                # 构建安全的列名
                status_column = f"{stage_type}_status"

                # 获取当前状态
                cursor.execute(f"SELECT {status_column} FROM test_cases WHERE id = ?", (case_id,))
                result = cursor.fetchone()
                if not result:
                    logger.error(f"用例不存在: {case_id}")
                    return False

                old_status = result[0]

                # 更新状态
                update_sql = f"UPDATE test_cases SET {status_column} = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?"
                cursor.execute(update_sql, (status, case_id))

                # 记录状态变更历史
                try:
                    cursor.execute('''
                        INSERT INTO case_status_history (
                            case_id, old_status, new_status, stage_type, changed_by
                        ) VALUES (?, ?, ?, ?, ?)
                    ''', (case_id, old_status, status, stage_type, changed_by or 'system'))
                except Exception as history_error:
                    # 如果历史表不存在，只记录警告，不影响主要功能
                    logger.warning(f"记录状态历史失败: {history_error}")

                conn.commit()
                logger.info(f"更新用例状态成功: {case_id} {stage_type} {old_status} -> {status}")
                return True

        except Exception as e:
            logger.error(f"更新用例状态失败: {str(e)}")
            return False

    @staticmethod
    def delete_test_case(case_id: int) -> bool:
        """
        删除用例

        Args:
            case_id: 用例ID

        Returns:
            bool: 是否删除成功
        """
        try:
            with get_db() as conn:
                cursor = conn.cursor()

                # 删除相关的历史记录
                try:
                    cursor.execute("DELETE FROM case_status_history WHERE case_id = ?", (case_id,))
                except Exception as history_error:
                    # 如果历史表不存在，只记录警告，不影响主要功能
                    logger.warning(f"删除状态历史失败: {history_error}")

                # 删除用例
                cursor.execute("DELETE FROM test_cases WHERE id = ?", (case_id,))

                if cursor.rowcount > 0:
                    conn.commit()
                    logger.info(f"删除用例成功: {case_id}")
                    return True
                else:
                    logger.warning(f"用例不存在: {case_id}")
                    return False

        except Exception as e:
            logger.error(f"删除用例失败: {str(e)}")
            return False

    @staticmethod
    def get_case_statistics(project_id: int = None) -> Dict[str, Any]:
        """
        获取用例统计信息

        Args:
            project_id: 项目ID，如果为None则统计所有项目

        Returns:
            Dict: 统计信息
        """
        try:
            with get_db() as conn:
                cursor = conn.cursor()

                where_clause = "WHERE project_id = ?" if project_id else ""
                params = [project_id] if project_id else []

                # 基础统计 - 与仪表盘保持一致的状态格式
                cursor.execute(f"""
                    SELECT
                        COUNT(*) as total_cases,
                        -- 按级别统计，每个用例在每个级别只计算一次
                        -- Subsys级别：优先考虑PASS状态
                        SUM(CASE WHEN subsys_status = 'PASS' THEN 1 ELSE 0 END) as subsys_pass,
                        SUM(CASE WHEN subsys_status = 'Pending' AND subsys_status != 'PASS' THEN 1 ELSE 0 END) as subsys_pending,
                        SUM(CASE WHEN subsys_status = 'On-Going' AND subsys_status != 'PASS' THEN 1 ELSE 0 END) as subsys_ongoing,
                        SUM(CASE WHEN subsys_status NOT IN ('PASS', 'Pending', 'On-Going', 'N/A') AND subsys_status IS NOT NULL THEN 1 ELSE 0 END) as subsys_fail,
                        -- TOP级别：优先考虑PASS状态
                        SUM(CASE WHEN top_status = 'PASS' THEN 1 ELSE 0 END) as top_pass,
                        SUM(CASE WHEN top_status = 'Pending' AND top_status != 'PASS' THEN 1 ELSE 0 END) as top_pending,
                        SUM(CASE WHEN top_status = 'On-Going' AND top_status != 'PASS' THEN 1 ELSE 0 END) as top_ongoing,
                        SUM(CASE WHEN top_status NOT IN ('PASS', 'Pending', 'On-Going', 'N/A') AND top_status IS NOT NULL THEN 1 ELSE 0 END) as top_fail,
                        -- POST阶段统计（用于兼容性）
                        SUM(CASE WHEN post_subsys_status = 'PASS' THEN 1 ELSE 0 END) as post_subsys_pass,
                        SUM(CASE WHEN post_top_status = 'PASS' THEN 1 ELSE 0 END) as post_top_pass,
                        -- 总体状态统计（任一阶段通过即算通过）
                        SUM(CASE WHEN subsys_status = 'PASS' OR top_status = 'PASS'
                                   OR post_subsys_status = 'PASS' OR post_top_status = 'PASS'
                            THEN 1 ELSE 0 END) as total_passed_any,
                        -- 进行中统计（任一阶段进行中且没有通过）
                        SUM(CASE WHEN (subsys_status = 'On-Going' OR top_status = 'On-Going'
                                      OR post_subsys_status = 'On-Going' OR post_top_status = 'On-Going')
                                  AND subsys_status != 'PASS' AND top_status != 'PASS'
                                  AND post_subsys_status != 'PASS' AND post_top_status != 'PASS'
                            THEN 1 ELSE 0 END) as total_ongoing_any
                    FROM test_cases {where_clause}
                """, params)

                stats = dict(cursor.fetchone())

                # 计算进度百分比 - 基于新的统计逻辑
                total = stats['total_cases']
                if total > 0:
                    # Subsys级别进度（通过的用例数/总数）
                    stats['subsys_progress'] = round(stats['subsys_pass'] / total * 100, 2)
                    # TOP级别进度（通过的用例数/总数）
                    stats['top_progress'] = round(stats['top_pass'] / total * 100, 2)
                    # POST阶段进度计算
                    stats['post_subsys_progress'] = round(stats['post_subsys_pass'] / total * 100, 2)
                    stats['post_top_progress'] = round(stats['post_top_pass'] / total * 100, 2)
                else:
                    stats['subsys_progress'] = 0
                    stats['top_progress'] = 0
                    stats['post_subsys_progress'] = 0
                    stats['post_top_progress'] = 0

                # 添加总计统计（与仪表盘保持一致）
                stats['total_passed'] = stats['total_passed_any']  # 使用任一阶段通过的统计
                stats['total_ongoing'] = stats['total_ongoing_any']  # 使用任一阶段进行中的统计

                # 计算待处理和失败的用例数
                cursor.execute(f'''
                    SELECT
                        SUM(CASE WHEN (subsys_status = 'Pending' OR top_status = 'Pending'
                                      OR post_subsys_status = 'Pending' OR post_top_status = 'Pending')
                                  AND subsys_status != 'PASS' AND top_status != 'PASS'
                                  AND post_subsys_status != 'PASS' AND post_top_status != 'PASS'
                                  AND subsys_status != 'On-Going' AND top_status != 'On-Going'
                                  AND post_subsys_status != 'On-Going' AND post_top_status != 'On-Going'
                            THEN 1 ELSE 0 END) as total_pending,
                        SUM(CASE WHEN (subsys_status NOT IN ('PASS', 'Pending', 'On-Going', 'N/A')
                                      OR top_status NOT IN ('PASS', 'Pending', 'On-Going', 'N/A')
                                      OR post_subsys_status NOT IN ('PASS', 'Pending', 'On-Going', 'N/A')
                                      OR post_top_status NOT IN ('PASS', 'Pending', 'On-Going', 'N/A'))
                                  AND subsys_status != 'PASS' AND top_status != 'PASS'
                                  AND post_subsys_status != 'PASS' AND post_top_status != 'PASS'
                            THEN 1 ELSE 0 END) as total_failed
                    FROM test_cases {where_clause}
                ''', params)
                additional_stats = dict(cursor.fetchone())

                stats['total_pending'] = additional_stats['total_pending'] or 0
                stats['total_failed'] = additional_stats['total_failed'] or 0

                # 为了与仪表盘API保持一致，添加cases结构
                stats['cases'] = {
                    'total': stats['total_cases'],
                    'passed': stats['total_passed'],
                    'pending': stats['total_pending'],
                    'running': stats['total_ongoing'],
                    'pass_rate': round((stats['total_passed'] / stats['total_cases'] * 100) if stats['total_cases'] > 0 else 0, 2),
                    'subsys': {
                        'passed': stats['subsys_pass'],
                        'pending': stats['subsys_pending'],
                        'running': stats['subsys_ongoing']
                    },
                    'top': {
                        'passed': stats['top_pass'],
                        'pending': stats['top_pending'],
                        'running': stats['top_ongoing']
                    }
                }

                return stats

        except Exception as e:
            logger.error(f"获取用例统计失败: {str(e)}")
            return {}

    @staticmethod
    def batch_import_cases(cases_data: List[Dict[str, Any]], project_id: int = None) -> Dict[str, Any]:
        """
        批量导入用例（支持插入新用例和更新现有用例）

        Args:
            cases_data: 用例数据列表
            project_id: 项目ID

        Returns:
            Dict: 导入结果统计
        """
        success_count = 0
        errors = []
        updated_count = 0
        inserted_count = 0

        try:
            with get_db() as conn:
                cursor = conn.cursor()

                # 获取或创建项目
                if project_id is None:
                    cursor.execute("SELECT id FROM projects WHERE name = '默认项目' LIMIT 1")
                    result = cursor.fetchone()
                    if result:
                        project_id = result['id']
                    else:
                        cursor.execute('''
                            INSERT INTO projects (name, subsystem, description)
                            VALUES (?, ?, ?)
                        ''', ('默认项目', 'default', '系统默认项目'))
                        project_id = cursor.lastrowid

                # 批量处理用例
                for i, case_data in enumerate(cases_data, 1):
                    try:
                        # 检查用例名称是否已存在
                        cursor.execute('''
                            SELECT id, subsys_phase, subsys_status, top_phase, top_status,
                                   post_subsys_phase, post_subsys_status, post_top_phase, post_top_status
                            FROM test_cases WHERE case_name = ? AND project_id = ?
                        ''', (case_data['case_name'], project_id))

                        existing_case = cursor.fetchone()

                        if existing_case:
                            # 用例已存在，检查是否需要更新
                            case_id = existing_case['id']
                            needs_update = False
                            update_fields = []
                            update_values = []

                            # 定义需要比较的字段映射（新字段名 -> 数据库字段名）
                            phase_status_fields = {
                                'subsys_phase': 'subsys_phase',
                                'subsys_status': 'subsys_status',
                                'top_phase': 'top_phase',
                                'top_status': 'top_status',
                                'post_subsys_phase': 'post_subsys_phase',
                                'post_subsys_status': 'post_subsys_status',
                                'post_top_phase': 'post_top_phase',
                                'post_top_status': 'post_top_status'
                            }

                            # 检查每个字段是否有变化
                            for case_field, db_field in phase_status_fields.items():
                                new_value = case_data.get(case_field, '')
                                old_value = existing_case[db_field] or ''

                                # 标准化比较值
                                if new_value != old_value:
                                    needs_update = True
                                    update_fields.append(f"{db_field} = ?")
                                    update_values.append(new_value)

                            # 检查基本字段
                            basic_fields = ['category', 'test_areas', 'test_scope', 'function_point', 'check_point', 'cover', 'owner']
                            for field in basic_fields:
                                if case_data.get(field):
                                    needs_update = True
                                    update_fields.append(f"{field} = ?")
                                    update_values.append(case_data.get(field, ''))

                            if needs_update:
                                # 更新现有用例
                                update_fields.append("updated_at = CURRENT_TIMESTAMP")
                                update_sql = f"UPDATE test_cases SET {', '.join(update_fields)} WHERE id = ?"
                                update_values.append(case_id)

                                cursor.execute(update_sql, update_values)
                                updated_count += 1
                                success_count += 1
                                logger.info(f"更新用例: {case_data['case_name']} (ID: {case_id})")
                            else:
                                # 无需更新
                                logger.debug(f"用例无变化，跳过: {case_data['case_name']}")
                        else:
                            # 插入新用例
                            cursor.execute('''
                                INSERT INTO test_cases (
                                    project_id, category, number, test_areas, test_scope, function_point,
                                    check_point, cover, case_name, start_time, end_time, actual_time, owner,
                                    subsys_phase, subsys_status, top_phase, top_status,
                                    post_subsys_phase, post_subsys_status, post_top_phase, post_top_status,
                                    remarks, subsys_stage, top_stage, test_process, coverage_point
                                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ''', (
                                project_id,
                                case_data.get('category', ''),
                                case_data.get('number', ''),
                                case_data.get('test_areas', ''),
                                case_data.get('test_scope', ''),
                                case_data.get('function_point', ''),
                                case_data.get('check_point', ''),
                                case_data.get('cover', ''),
                                case_data['case_name'],
                                case_data.get('start_time'),
                                case_data.get('end_time'),
                                case_data.get('actual_time'),
                                case_data.get('owner', ''),
                                # 新的验证阶段字段
                                case_data.get('subsys_phase', 'N/A'),
                                case_data.get('subsys_status', 'Pending'),
                                case_data.get('top_phase', 'N/A'),
                                case_data.get('top_status', 'Pending'),
                                case_data.get('post_subsys_phase', ''),
                                case_data.get('post_subsys_status', 'Pending'),
                                case_data.get('post_top_phase', ''),
                                case_data.get('post_top_status', 'Pending'),
                                case_data.get('remarks', ''),
                                # 兼容旧格式字段
                                case_data.get('subsys_stage', ''),
                                case_data.get('top_stage', ''),
                                case_data.get('test_process', ''),
                                case_data.get('coverage_point', '')
                            ))

                            inserted_count += 1
                            success_count += 1
                            logger.info(f"插入新用例: {case_data['case_name']}")

                    except Exception as e:
                        errors.append(f"第{i}行: 处理失败 - {str(e)}")
                        logger.error(f"处理用例失败 {case_data.get('case_name', 'Unknown')}: {str(e)}")

                conn.commit()
                logger.info(f"批量导入完成: 新增{inserted_count}条，更新{updated_count}条，失败{len(errors)}条")

        except Exception as e:
            logger.error(f"批量导入用例失败: {str(e)}")
            errors.append(f"批量导入失败: {str(e)}")

        # 返回详细的统计信息
        return {
            'success_count': success_count,
            'inserted_count': inserted_count,
            'updated_count': updated_count,
            'errors': errors
        }
