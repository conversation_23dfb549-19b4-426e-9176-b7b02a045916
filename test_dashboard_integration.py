#!/usr/bin/env python3
"""
仪表盘集成测试脚本

测试仿真状态自动更新和统计功能的端到端集成
"""

import os
import sys
import time
import json
import requests
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DashboardIntegrationTest:
    """仪表盘集成测试类"""
    
    def __init__(self, dashboard_port=5001):
        self.dashboard_port = dashboard_port
        self.base_url = f"http://127.0.0.1:{dashboard_port}"
        self.test_cases = []
    
    def test_api_connectivity(self):
        """测试API连接性"""
        print("\n=== 测试API连接性 ===")
        
        try:
            # 测试健康检查API
            response = requests.get(f"{self.base_url}/api/health", timeout=5)
            if response.status_code == 200:
                print("✅ 仪表盘服务连接正常")
                return True
            else:
                print(f"❌ 仪表盘服务响应异常: HTTP {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ 仪表盘服务连接失败: {e}")
            return False
    
    def test_runsim_update_api(self):
        """测试RunSim状态更新API"""
        print("\n=== 测试RunSim状态更新API ===")
        
        test_cases = [
            {
                'name': 'test_subsys_case',
                'command': 'runsim -base subsys -block dv/test -case test_subsys_case -rundir work/subsys_test',
                'expected_type': 'subsys'
            },
            {
                'name': 'test_top_case',
                'command': 'runsim -base top -block udtb/usvp -case test_top_case -rundir work/top_test',
                'expected_type': 'top'
            },
            {
                'name': 'test_post_subsys_case',
                'command': 'runsim -base subsys -block dv/test -case test_post_subsys_case -post post_sim',
                'expected_type': 'post_subsys'
            },
            {
                'name': 'test_post_top_case',
                'command': 'runsim -base top -case test_post_top_case -post post_analysis',
                'expected_type': 'post_top'
            }
        ]
        
        success_count = 0
        
        for test_case in test_cases:
            print(f"\n测试用例: {test_case['name']}")
            print(f"命令: {test_case['command']}")
            print(f"预期类型: {test_case['expected_type']}")
            
            # 测试仿真开始状态更新
            start_data = {
                'case_name': test_case['name'],
                'status': 'On-Going',
                'command': test_case['command'],
                'start_time': datetime.now().isoformat(),
                'updated_by': 'integration_test'
            }
            
            try:
                response = requests.post(
                    f"{self.base_url}/api/testplan/update_from_runsim",
                    json=start_data,
                    timeout=10
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        print(f"✅ 开始状态更新成功: {result.get('data', {}).get('case_type')}")
                        
                        # 等待一段时间
                        time.sleep(2)
                        
                        # 测试仿真成功结束状态更新
                        success_data = {
                            'case_name': test_case['name'],
                            'status': 'PASS',
                            'end_time': datetime.now().isoformat(),
                            'message': '集成测试完成',
                            'updated_by': 'integration_test'
                        }
                        
                        response = requests.post(
                            f"{self.base_url}/api/testplan/update_from_runsim",
                            json=success_data,
                            timeout=10
                        )
                        
                        if response.status_code == 200:
                            result = response.json()
                            if result.get('success'):
                                print(f"✅ 结束状态更新成功")
                                success_count += 1
                            else:
                                print(f"❌ 结束状态更新失败: {result.get('error')}")
                        else:
                            print(f"❌ 结束状态更新请求失败: HTTP {response.status_code}")
                    else:
                        print(f"❌ 开始状态更新失败: {result.get('error')}")
                else:
                    print(f"❌ 开始状态更新请求失败: HTTP {response.status_code}")
                    
            except requests.exceptions.RequestException as e:
                print(f"❌ API请求异常: {e}")
        
        print(f"\n状态更新测试完成: {success_count}/{len(test_cases)} 成功")
        return success_count == len(test_cases)
    
    def test_pass_rate_api(self):
        """测试用例通过率统计API"""
        print("\n=== 测试用例通过率统计API ===")
        
        test_params = [
            {'unit': 'day', 'days': 7},
            {'unit': 'day', 'days': 30},
            {'unit': 'week', 'days': 30}
        ]
        
        success_count = 0
        
        for params in test_params:
            print(f"\n测试参数: {params}")
            
            try:
                response = requests.get(
                    f"{self.base_url}/api/dashboard/case_pass_rate",
                    params=params,
                    timeout=10
                )
                
                if response.status_code == 200:
                    data = response.json()
                    
                    # 验证数据结构
                    required_fields = ['labels', 'pass_count', 'total_count', 'pass_rate', 'cumulative']
                    missing_fields = [field for field in required_fields if field not in data]
                    
                    if not missing_fields:
                        print(f"✅ API响应正常，数据结构完整")
                        print(f"   标签数量: {len(data.get('labels', []))}")
                        print(f"   累计统计: {data.get('cumulative', {})}")
                        success_count += 1
                    else:
                        print(f"❌ 数据结构不完整，缺少字段: {missing_fields}")
                else:
                    print(f"❌ API请求失败: HTTP {response.status_code}")
                    
            except requests.exceptions.RequestException as e:
                print(f"❌ API请求异常: {e}")
        
        print(f"\n通过率API测试完成: {success_count}/{len(test_params)} 成功")
        return success_count == len(test_params)
    
    def test_dashboard_page(self):
        """测试仪表盘页面"""
        print("\n=== 测试仪表盘页面 ===")
        
        try:
            response = requests.get(f"{self.base_url}/dashboard", timeout=10)
            
            if response.status_code == 200:
                content = response.text
                
                # 检查关键元素是否存在
                required_elements = [
                    'passRateChart',  # 用例通过率图表
                    'cumulative-total-cases',  # 累计统计元素
                    'passRateUnit',  # 时间单位切换
                    'passRateMode'   # 显示模式切换
                ]
                
                missing_elements = []
                for element in required_elements:
                    if element not in content:
                        missing_elements.append(element)
                
                if not missing_elements:
                    print("✅ 仪表盘页面加载正常，包含所有必要元素")
                    return True
                else:
                    print(f"❌ 仪表盘页面缺少元素: {missing_elements}")
                    return False
            else:
                print(f"❌ 仪表盘页面加载失败: HTTP {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 页面请求异常: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("开始仪表盘集成测试...")
        print(f"测试时间: {datetime.now()}")
        print(f"仪表盘地址: {self.base_url}")
        
        test_results = []
        
        # 测试API连接性
        test_results.append(('API连接性', self.test_api_connectivity()))
        
        # 测试RunSim状态更新API
        test_results.append(('RunSim状态更新API', self.test_runsim_update_api()))
        
        # 测试用例通过率统计API
        test_results.append(('用例通过率统计API', self.test_pass_rate_api()))
        
        # 测试仪表盘页面
        test_results.append(('仪表盘页面', self.test_dashboard_page()))
        
        # 输出测试结果
        print("\n" + "="*50)
        print("测试结果汇总:")
        print("="*50)
        
        passed_tests = 0
        total_tests = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if result:
                passed_tests += 1
        
        print(f"\n总体结果: {passed_tests}/{total_tests} 测试通过")
        
        if passed_tests == total_tests:
            print("\n🎉 所有测试通过！仪表盘自动状态更新功能集成成功！")
        else:
            print(f"\n⚠️  有 {total_tests - passed_tests} 个测试失败，请检查相关功能")
        
        return passed_tests == total_tests


def main():
    """主函数"""
    try:
        # 创建测试实例
        tester = DashboardIntegrationTest()
        
        # 运行所有测试
        success = tester.run_all_tests()
        
        # 返回适当的退出码
        sys.exit(0 if success else 1)
        
    except Exception as e:
        logger.error(f"测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
