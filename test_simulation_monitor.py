#!/usr/bin/env python3
"""
仿真监听器测试脚本

测试仿真状态监听和仪表盘更新功能
"""

import os
import sys
import time
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.simulation_monitor import SimulationMonitor, CommandParser
from utils.dashboard_updater import DashboardUpdater, initialize_dashboard_updater

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_command_parser():
    """测试命令解析器"""
    print("\n=== 测试命令解析器 ===")
    
    test_commands = [
        "runsim -base top -block udtb/usvp -case test_case1 -rundir work/test1",
        "runsim -base apcpu_sys -block udtb/apcpu_sys/clk -case test_case2 -post post_sim",
        "runsim -block top -case test_case3",
        "runsim -base subsys -block dv/subsys -case test_case4 -rundir work/subsys_test"
    ]
    
    for cmd in test_commands:
        print(f"\n命令: {cmd}")
        params = CommandParser.parse_command(cmd)
        case_type = CommandParser.determine_case_type(params)
        print(f"参数: {params}")
        print(f"用例类型: {case_type}")


def test_dashboard_updater():
    """测试仪表盘更新器"""
    print("\n=== 测试仪表盘更新器 ===")
    
    # 初始化更新器
    updater = initialize_dashboard_updater()
    
    # 测试连接
    print("测试仪表盘连接...")
    connection_ok = updater.test_connection()
    print(f"连接状态: {'正常' if connection_ok else '失败'}")
    
    if not connection_ok:
        print("警告: 仪表盘连接失败，请确保仪表盘服务正在运行")
        return
    
    # 测试用例开始更新
    print("\n测试用例开始状态更新...")
    test_command = "runsim -base top -block udtb/usvp -case test_monitor_case -rundir work/test_monitor"
    test_params = CommandParser.parse_command(test_command)
    
    updater.update_case_start("test_monitor_case", test_command, test_params)
    
    # 等待一段时间
    print("等待5秒...")
    time.sleep(5)
    
    # 测试用例结束更新
    print("测试用例结束状态更新...")
    updater.update_case_finish("test_monitor_case", True, "测试完成")


def test_simulation_monitor():
    """测试仿真监听器"""
    print("\n=== 测试仿真监听器 ===")
    
    # 创建仿真监听器
    monitor = SimulationMonitor()
    
    # 设置仪表盘更新器
    updater = initialize_dashboard_updater()
    monitor.set_dashboard_updater(updater)
    
    # 测试仿真开始事件
    print("模拟仿真开始事件...")
    test_case = "test_simulation_case"
    test_command = "runsim -base subsys -block dv/test -case test_simulation_case -rundir work/sim_test"
    
    monitor.on_execution_started(test_case, test_command)
    
    # 等待一段时间
    print("等待3秒...")
    time.sleep(3)
    
    # 测试仿真结束事件
    print("模拟仿真结束事件...")
    monitor.on_execution_finished(test_case, 0)  # 退出码0表示成功


def test_log_monitoring():
    """测试日志监控功能"""
    print("\n=== 测试日志监控功能 ===")
    
    # 创建测试目录和日志文件
    test_dir = "test_log_monitor"
    log_file = os.path.join(test_dir, "irun_sim.log")
    
    try:
        # 创建测试目录
        os.makedirs(test_dir, exist_ok=True)
        
        # 创建测试日志文件
        with open(log_file, 'w') as f:
            f.write("仿真开始...\n")
            f.write("正在执行测试用例...\n")
            f.write("测试进行中...\n")
        
        print(f"创建测试日志文件: {log_file}")
        
        # 导入日志监控器
        from utils.simulation_monitor import LogMonitor
        
        log_monitor = LogMonitor()
        
        def log_callback(case_name, success, message):
            print(f"日志监控回调: {case_name} - {'成功' if success else '失败'} - {message}")
        
        # 开始监控
        print("开始监控日志文件...")
        log_monitor.start_monitoring("test_log_case", test_dir, log_callback)
        
        # 等待一段时间
        time.sleep(2)
        
        # 模拟写入成功标志
        print("写入成功标志...")
        with open(log_file, 'a') as f:
            f.write("SPRD_PASSED: 测试通过\n")
            f.write("仿真完成\n")
        
        # 等待监控检测到变化
        time.sleep(3)
        
        # 停止监控
        log_monitor.stop_monitoring("test_log_case")
        
    except Exception as e:
        print(f"日志监控测试失败: {e}")
    finally:
        # 清理测试文件
        try:
            if os.path.exists(log_file):
                os.remove(log_file)
            if os.path.exists(test_dir):
                os.rmdir(test_dir)
            print("清理测试文件完成")
        except:
            pass


def main():
    """主测试函数"""
    print("开始测试仿真监听器功能...")
    print(f"测试时间: {datetime.now()}")
    
    try:
        # 测试命令解析器
        test_command_parser()
        
        # 测试仪表盘更新器
        test_dashboard_updater()
        
        # 测试仿真监听器
        test_simulation_monitor()
        
        # 测试日志监控功能
        test_log_monitoring()
        
        print("\n=== 所有测试完成 ===")
        
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
